// pages/game-main/game-main.js - 丢粪大作战 PRO (纯图片版)

Page({
  data: {
    // 游戏状态
    score: 0,
    currentLevel: 1,
    chancesLeft: 3,
    isGameStarted: false,
    levelCompleted: false,
    showResultModal: false,
    showLevelIntro: true,
    showDebug: false,

    // 交互状态
    isDragging: false,
    power: 0,
    angle: 45,

    // 结果数据
    resultType: '',
    resultMessage: '',
    lastScore: 0,

    // 关卡配置
    levelConfig: {
      icon: '🎯',
      title: '第1关 - 新手挑战',
      description: '瞄准云纸老板，试试手感！',
      tips: ['拖拽大粪调整角度和力度', '松手发射', '击中目标获得奖励']
    },

    // 游戏元素位置（rpx单位，基于750rpx宽度设计）
    bossX: 450,          // Boss在右上区域
    bossY: 300,
    bossOffsetX: 0,
    bossImageSrc: '/images/boss-normal.png',

    slingshotX: 350,     // 弹弓在底部中央
    slingshotY: 1100,

    poopX: 362,          // 大粪在弹弓附近 (350 + 12)
    poopY: 1088,         // 稍微在弹弓上方
    poopVisible: true,
    isFlying: false,

    platformX: 400,      // 平台在Boss下方
    platformY: 450,

    // 轨迹预判点
    trajectoryPoints: [],

    // 弹弓皮筋线样式
    slingshotLineStyle: ''
  },

  // 游戏核心变量
  screenWidth: 375,
  screenHeight: 667,

  // 游戏对象（像素单位）
  boss: {
    x: 250,
    y: 100,
    width: 100,
    height: 120,
    direction: 1,
    speed: 1,
    hasHelmet: false,
    movePattern: 'normal',
    isHit: false,
    hitTime: 0
  },

  slingshot: {
    x: 150,
    y: 250,
    width: 60,
    height: 80
  },

  poop: {
    x: 162,
    y: 238,
    width: 24,
    height: 24,
    vx: 0,
    vy: 0,
    isFlying: false,
    trail: []
  },

  trajectory: [],
  gameLoop: null,
  touchData: null,

  // 关卡配置
  levels: [
    {
      level: 1,
      icon: '🎯',
      title: '第1关 - 新手挑战',
      description: '瞄准云纸老板，试试手感！',
      tips: ['拖拽大粪调整角度和力度', '松手发射', '击中目标获得奖励'],
      bossSpeed: 1,
      hasHelmet: false,
      movePattern: 'normal'
    },
    {
      level: 2,
      icon: '🛡️',
      title: '第2关 - 安全帽挑战',
      description: '老板戴上了安全帽，瞄准下半身！',
      tips: ['安全帽会保护头部', '瞄准腰部以下区域', '调整角度很重要'],
      bossSpeed: 1.5,
      hasHelmet: true,
      movePattern: 'normal'
    },
    {
      level: 3,
      icon: '💨',
      title: '第3关 - 闪避大师',
      description: '老板移动更快了，考验你的预判！',
      tips: ['老板移动速度加快', '需要预判移动轨迹', '时机很重要'],
      bossSpeed: 3,
      hasHelmet: true,
      movePattern: 'zigzag'
    }
  ],

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function() {
    console.log('=== 丢粪大作战 PRO 纯图片版加载 ===');
    this.initLevel(1);

    // 立即设置初始位置（基于750rpx设计稿）
    this.setData({
      bossX: 450,          // Boss在右上区域，适合屏幕显示
      bossY: 300,
      bossImageSrc: '/images/boss-normal.png',
      slingshotX: 350,     // 弹弓居中底部
      slingshotY: 1100,
      poopX: 362,          // 大粪在弹弓附近
      poopY: 1088,
      platformX: 400,      // 平台在Boss下方
      platformY: 450,
      poopVisible: true
    });

    console.log('🎮 初始位置已设置');
  },

  /**
   * 生命周期函数--监听页面渲染完成
   */
  onReady: function() {
    console.log('📱 页面渲染完成，开始初始化');

    // 获取屏幕尺寸
    const systemInfo = wx.getSystemInfoSync();
    this.screenWidth = systemInfo.windowWidth;
    this.screenHeight = systemInfo.windowHeight;

    console.log('📱 屏幕尺寸:', this.screenWidth, 'x', this.screenHeight);

    // 初始化游戏对象位置
    this.setupGameObjects();
  },

  /**
   * 初始化关卡
   */
  initLevel: function(level) {
    const levelConfig = this.levels[level - 1];

    this.setData({
      currentLevel: level,
      chancesLeft: 3,
      levelCompleted: false,
      showLevelIntro: false,  // 暂时关闭，让用户先看布局
      isGameStarted: false,
      showResultModal: false,
      levelConfig: levelConfig
    });

    // 设置Boss配置
    this.boss.speed = levelConfig.bossSpeed;
    this.boss.hasHelmet = levelConfig.hasHelmet;
    this.boss.movePattern = levelConfig.movePattern;
    this.boss.isHit = false;

    // 更新Boss图片
    this.updateBossImage();

    console.log('🎯 初始化关卡', level, levelConfig);
  },

  /**
   * 开始关卡
   */
  startLevel: function() {
    this.setData({
      showLevelIntro: false,
      isGameStarted: true
    });

    this.resetPoop();
    this.startGameLoop();

    console.log('🎮 开始关卡', this.data.currentLevel);
  },

  /**
   * 设置游戏对象位置
   */
  setupGameObjects: function() {
    // 根据屏幕尺寸计算位置
    const scale = this.screenWidth / 375; // 基于375px设计稿缩放

    // 弹弓位置（底部中央）
    this.slingshot.x = this.screenWidth / 2 - 30;
    this.slingshot.y = this.screenHeight * 0.7;

    // Boss位置（右上区域）
    this.boss.x = this.screenWidth * 0.65;
    this.boss.y = this.screenHeight * 0.25;

    // 更新页面显示
    this.updatePositions();
    this.resetPoop();

    console.log('📍 游戏对象位置设置完成');
    console.log('弹弓:', this.slingshot.x, this.slingshot.y);
    console.log('Boss:', this.boss.x, this.boss.y);
  },

  /**
   * 更新所有位置到页面
   */
  updatePositions: function() {
    this.setData({
      // Boss位置（转换为rpx）
      bossX: this.boss.x * 2,
      bossY: this.boss.y * 2,
      bossOffsetX: this.boss.offsetX || 0,

      // 弹弓位置
      slingshotX: this.slingshot.x * 2,
      slingshotY: this.slingshot.y * 2,

      // 大粪位置
      poopX: this.poop.x * 2,
      poopY: this.poop.y * 2,
      poopVisible: !this.poop.isFlying || this.poop.x > -50,
      isFlying: this.poop.isFlying,

      // 平台位置
      platformX: (this.boss.x - 20) * 2,
      platformY: (this.boss.y + this.boss.height) * 2
    });
  },

  /**
   * 更新Boss图片
   */
  updateBossImage: function() {
    let imageSrc = '/images/boss-normal.png';

    if (this.boss.isHit) {
      imageSrc = '/images/boss-hit.png';
    } else if (this.boss.hasHelmet) {
      imageSrc = '/images/boss-helmet.png';
    }

    this.setData({ bossImageSrc: imageSrc });
  },

  /**
   * 重置大粪位置
   */
  resetPoop: function() {
    // 确保游戏对象已初始化
    if (!this.slingshot || !this.poop) {
      console.log('⚠️ 游戏对象未初始化，先初始化...');
      this.setupGameObjects();
    }

    this.poop.x = this.slingshot.x + this.slingshot.width / 2 - this.poop.width / 2;
    this.poop.y = this.slingshot.y - this.poop.height / 2;
    this.poop.vx = 0;
    this.poop.vy = 0;
    this.poop.isFlying = false;
    this.poop.trail = [];
    this.trajectory = [];

    this.setData({
      trajectoryPoints: [],
      slingshotLineStyle: ''
    });

    this.updatePositions();
  },

  /**
   * 触摸开始
   */
  onTouchStart: function(e) {
    if (this.poop.isFlying || !this.data.isGameStarted) return;

    const touch = e.touches[0];
    const poopCenterX = this.poop.x + this.poop.width / 2;
    const poopCenterY = this.poop.y + this.poop.height / 2;

    // 检查是否点击了大粪
    const distance = Math.sqrt(
      Math.pow(touch.x - poopCenterX, 2) +
      Math.pow(touch.y - poopCenterY, 2)
    );

    if (distance < 40) {
      this.touchData = {
        startX: touch.x,
        startY: touch.y,
        currentX: touch.x,
        currentY: touch.y
      };

      this.setData({ isDragging: true });
      console.log('🖱️ 开始拖拽大粪');
    }
  },

  /**
   * 触摸移动
   */
  onTouchMove: function(e) {
    if (!this.data.isDragging) return;

    const touch = e.touches[0];
    this.touchData.currentX = touch.x;
    this.touchData.currentY = touch.y;

    // 计算拖拽偏移
    const deltaX = this.touchData.startX - this.touchData.currentX;
    const deltaY = this.touchData.startY - this.touchData.currentY;

    // 限制拖拽范围
    const maxDrag = 100;
    const dragDistance = Math.sqrt(deltaX * deltaX + deltaY * deltaY);
    let limitedDeltaX = deltaX;
    let limitedDeltaY = deltaY;

    if (dragDistance > maxDrag) {
      const ratio = maxDrag / dragDistance;
      limitedDeltaX *= ratio;
      limitedDeltaY *= ratio;
    }

    // 更新大粪位置
    this.poop.x = this.slingshot.x + this.slingshot.width / 2 - limitedDeltaX - this.poop.width / 2;
    this.poop.y = this.slingshot.y - this.poop.height / 2 - limitedDeltaY;

    // 计算力度和角度
    const power = Math.min(dragDistance * 2, 100);
    const angle = Math.atan2(-limitedDeltaY, limitedDeltaX) * 180 / Math.PI;

    this.setData({
      power: power,
      angle: angle
    });

    // 更新位置显示
    this.updatePositions();

    // 计算轨迹预判
    this.calculateTrajectory(limitedDeltaX, limitedDeltaY, power);

    // 更新弹弓皮筋线
    this.updateSlingshotLine();
  },

  /**
   * 触摸结束
   */
  onTouchEnd: function() {
    if (!this.data.isDragging) return;

    this.setData({
      isDragging: false,
      trajectoryPoints: [],
      slingshotLineStyle: ''
    });

    if (this.data.power > 10) {
      this.launchPoop();
    } else {
      this.resetPoop();
    }
  },

  /**
   * 计算轨迹预判
   */
  calculateTrajectory: function(deltaX, deltaY, power) {
    const trajectoryPoints = [];

    const startX = this.poop.x + this.poop.width / 2;
    const startY = this.poop.y + this.poop.height / 2;

    const vx = deltaX * power * 0.15;
    const vy = deltaY * power * 0.15;

    const gravity = 0.8;
    const steps = 15;

    for (let i = 0; i < steps; i++) {
      const t = i * 0.5;
      const x = startX + vx * t;
      const y = startY + vy * t + 0.5 * gravity * t * t;

      if (y > this.screenHeight) break;

      trajectoryPoints.push({
        x: x * 2, // 转换为rpx
        y: y * 2,
        opacity: 0.8 - (i / steps) * 0.6
      });
    }

    this.setData({ trajectoryPoints });
  },

  /**
   * 更新弹弓皮筋线
   */
  updateSlingshotLine: function() {
    const slingshotCenterX = this.slingshot.x + this.slingshot.width / 2;
    const slingshotCenterY = this.slingshot.y + 10;
    const poopCenterX = this.poop.x + this.poop.width / 2;
    const poopCenterY = this.poop.y + this.poop.height / 2;

    const dx = poopCenterX - slingshotCenterX;
    const dy = poopCenterY - slingshotCenterY;
    const distance = Math.sqrt(dx * dx + dy * dy);
    const angle = Math.atan2(dy, dx) * 180 / Math.PI;

    const lineStyle = `
      left: ${slingshotCenterX * 2}rpx;
      top: ${slingshotCenterY * 2}rpx;
      width: ${distance * 2}rpx;
      transform: rotate(${angle}deg);
    `;

    this.setData({ slingshotLineStyle: lineStyle });
  },

  /**
   * 发射大粪
   */
  launchPoop: function() {
    const deltaX = this.touchData.startX - this.touchData.currentX;
    const deltaY = this.touchData.startY - this.touchData.currentY;

    this.poop.vx = deltaX * this.data.power * 0.15;
    this.poop.vy = deltaY * this.data.power * 0.15;
    this.poop.isFlying = true;

    // 播放发射音效
    wx.vibrateShort({ type: 'heavy' });

    console.log('🚀 发射大粪！力度:', this.data.power, '角度:', this.data.angle);
  },

  /**
   * 开始游戏循环
   */
  startGameLoop: function() {
    const that = this;

    function loop() {
      if (!that.data.isGameStarted || that.data.showResultModal) {
        return;
      }

      that.updateGame();

      that.gameLoop = setTimeout(loop, 33); // 30 FPS
    }

    loop();
    console.log('🔄 游戏循环启动');
  },

  /**
   * 更新游戏逻辑
   */
  updateGame: function() {
    // 更新Boss移动
    this.updateBoss();

    // 更新大粪物理
    if (this.poop.isFlying) {
      this.updatePoop();
    }

    // 更新页面显示
    this.updatePositions();
  },

  /**
   * 更新Boss
   */
  updateBoss: function() {
    const boss = this.boss;

    if (boss.movePattern === 'normal') {
      // 普通左右移动
      boss.x += boss.speed * boss.direction;

      if (boss.x <= 50 || boss.x + boss.width >= this.screenWidth - 50) {
        boss.direction *= -1;
      }
    } else if (boss.movePattern === 'zigzag') {
      // 之字形移动
      boss.x += boss.speed * boss.direction;
      boss.y += Math.sin(Date.now() * 0.01) * 0.5;

      if (boss.x <= 50 || boss.x + boss.width >= this.screenWidth - 50) {
        boss.direction *= -1;
      }
    }

    // 重置被击中状态
    if (boss.isHit) {
      boss.hitTime--;
      if (boss.hitTime <= 0) {
        boss.isHit = false;
        this.updateBossImage();
      }
    }
  },

  /**
   * 更新大粪
   */
  updatePoop: function() {
    const poop = this.poop;

    // 更新位置
    poop.x += poop.vx;
    poop.y += poop.vy;

    // 重力
    poop.vy += 0.8;

    // 碰撞检测
    if (this.checkCollision(poop, this.boss)) {
      this.onHit();
    }

    // 边界检测
    if (poop.y > this.screenHeight || poop.x < -50 || poop.x > this.screenWidth + 50) {
      this.onMiss();
    }
  },

  /**
   * 碰撞检测
   */
  checkCollision: function(obj1, obj2) {
    // 如果Boss戴安全帽，只检测下半身
    let checkY = obj2.y;
    let checkHeight = obj2.height;

    if (this.boss.hasHelmet) {
      checkY = obj2.y + obj2.height * 0.4; // 只检测下60%区域
      checkHeight = obj2.height * 0.6;
    }

    return obj1.x < obj2.x + obj2.width &&
           obj1.x + obj1.width > obj2.x &&
           obj1.y < checkY + checkHeight &&
           obj1.y + obj1.height > checkY;
  },

  /**
   * 击中目标
   */
  onHit: function() {
    this.poop.isFlying = false;

    // 设置Boss被击中状态
    this.boss.isHit = true;
    this.boss.hitTime = 60; // 60帧显示被击中状态
    this.updateBossImage();

    const score = 100 * this.data.currentLevel;
    this.setData({
      score: this.data.score + score,
      lastScore: score
    });

    console.log('🎯 击中目标！得分:', score);

    wx.vibrateShort({ type: 'heavy' });

    this.showResult('hit', '太棒了！一击命中！', score);
  },

  /**
   * 未击中目标
   */
  onMiss: function() {
    this.poop.isFlying = false;

    const chancesLeft = this.data.chancesLeft - 1;
    this.setData({ chancesLeft: chancesLeft });

    console.log('❌ 未击中，剩余机会:', chancesLeft);

    const messages = [
      '差点就中！再试试？',
      '角度再调整一下～',
      '力度可以再大一点！',
      '瞄准老板的身体！'
    ];

    const message = messages[Math.floor(Math.random() * messages.length)];
    this.showResult('miss', message);
  },

  /**
   * 显示结果
   */
  showResult: function(type, message, score = 0) {
    this.setData({
      showResultModal: true,
      resultType: type,
      resultMessage: message,
      lastScore: score
    });

    // 停止游戏循环
    if (this.gameLoop) {
      clearTimeout(this.gameLoop);
      this.gameLoop = null;
    }
  },

  /**
   * 重试投掷
   */
  retryShot: function() {
    this.setData({ showResultModal: false });
    this.resetPoop();
    this.startGameLoop();
  },

  /**
   * 重新开始关卡
   */
  restartLevel: function() {
    this.setData({
      chancesLeft: 3,
      showResultModal: false
    });
    this.resetPoop();
    this.startGameLoop();
  },

  /**
   * 下一关
   */
  nextLevel: function() {
    const nextLevel = this.data.currentLevel + 1;
    if (nextLevel <= this.levels.length) {
      this.initLevel(nextLevel);
    } else {
      wx.showModal({
        title: '恭喜通关！',
        content: '您已完成所有关卡！',
        showCancel: false
      });
    }
  },

  /**
   * 前往抽奖
   */
  goToLottery: function() {
    wx.navigateTo({
      url: '/pages/lottery/lottery'
    });
  },

  /**
   * 观看广告获得机会
   */
  watchAdForChance: function() {
    wx.showModal({
      title: '观看广告',
      content: '观看15秒广告获得3次额外机会？',
      success: (res) => {
        if (res.confirm) {
          // 模拟广告
          wx.showLoading({ title: '广告加载中...' });
          setTimeout(() => {
            wx.hideLoading();
            this.setData({
              chancesLeft: 3,
              showResultModal: false
            });
            this.resetPoop();
            this.startGameLoop();
            wx.showToast({
              title: '获得3次机会！',
              icon: 'success'
            });
          }, 2000);
        }
      }
    });
  },

  /**
   * 显示调试信息
   */
  showDebugInfo: function() {
    const info = `🎮 丢粪大作战 PRO 纯图片版

📊 游戏状态:
关卡: ${this.data.currentLevel}
机会: ${this.data.chancesLeft}/3
得分: ${this.data.score}
游戏开始: ${this.data.isGameStarted ? '是' : '否'}

📱 屏幕信息:
尺寸: ${this.screenWidth}x${this.screenHeight}px
当前rpx位置:
Boss: (${this.data.bossX}, ${this.data.bossY})rpx
弹弓: (${this.data.slingshotX}, ${this.data.slingshotY})rpx
大粪: (${this.data.poopX}, ${this.data.poopY})rpx

🖼️ 图片路径:
Boss: ${this.data.bossImageSrc}
背景: /images/bg.png
弹弓: /images/slingshot.png

✨ 技术特点:
- 纯图片实现，无Canvas
- 直接使用PNG素材
- CSS动画和定位
- 实时物理模拟`;

    wx.showModal({
      title: '🔧 调试信息',
      content: info,
      showCancel: true,
      cancelText: '强制刷新',
      confirmText: '知道了',
      success: (res) => {
        if (res.cancel) {
          this.forceRefresh();
        }
      }
    });
  },

  /**
   * 强制刷新游戏
   */
  forceRefresh: function() {
    console.log('🔄 强制刷新游戏...');

    // 重新设置所有位置
    this.setData({
      bossX: 500,
      bossY: 200,
      bossImageSrc: '/images/boss-normal.png',
      slingshotX: 300,
      slingshotY: 1000,
      poopX: 324,
      poopY: 976,
      platformX: 440,
      platformY: 440,
      poopVisible: true,
      showDebug: !this.data.showDebug
    });

    wx.showToast({
      title: '已强制刷新',
      icon: 'success'
    });
  },

  /**
   * 强制显示关卡介绍
   */
  forceShowLevelIntro: function() {
    console.log('🎯 强制显示关卡介绍弹窗');
    this.setData({
      showLevelIntro: true,
      isGameStarted: false,
      showResultModal: false
    });
  },

  /**
   * 检查布局状态
   */
  checkLayout: function() {
    console.log('📏 检查当前布局状态...');

    const layoutInfo = `🎮 丢粪大作战 PRO - 布局检查

📱 当前位置状态 (rpx):
• Boss: (${this.data.bossX}, ${this.data.bossY})
• 弹弓: (${this.data.slingshotX}, ${this.data.slingshotY})
• 大粪: (${this.data.poopX}, ${this.data.poopY})
• 平台: (${this.data.platformX}, ${this.data.platformY})

🎯 Boss状态:
• 图片: ${this.data.bossImageSrc}
• 可见: ${this.data.bossImageSrc ? '是' : '否'}

💩 大粪状态:
• 可见: ${this.data.poopVisible ? '是' : '否'}
• 飞行: ${this.data.isFlying ? '是' : '否'}

📏 屏幕设计参考:
• 宽度: 750rpx
• 建议Boss: (400-500, 200-400)
• 建议弹弓: (300-400, 1000-1200)`;

    wx.showModal({
      title: '📏 布局检查',
      content: layoutInfo,
      showCancel: true,
      cancelText: '优化布局',
      confirmText: '确定',
      success: (res) => {
        if (res.cancel) {
          this.optimizeLayout();
        }
      }
    });
  },

  /**
   * 优化布局
   */
  optimizeLayout: function() {
    console.log('✨ 正在优化布局...');

    // 设置更好的布局位置
    this.setData({
      bossX: 450,          // Boss稍微偏右
      bossY: 350,          // 适中高度
      slingshotX: 350,     // 弹弓居中
      slingshotY: 1150,    // 底部位置
      poopX: 362,          // 大粪在弹弓附近
      poopY: 1138,         // 稍微在弹弓上方
      platformX: 400,      // 平台在Boss下方
      platformY: 500,      // 给Boss留出空间
      poopVisible: true
    });

    // 重置物理对象位置
    this.boss.x = 225;     // rpx转px: 450/2 = 225
    this.boss.y = 175;     // rpx转px: 350/2 = 175
    this.slingshot.x = 175; // rpx转px: 350/2 = 175
    this.slingshot.y = 575; // rpx转px: 1150/2 = 575

    this.resetPoop();      // 重置大粪位置

    wx.showToast({
      title: '布局已优化！',
      icon: 'success',
      duration: 2000
    });

    console.log('✅ 布局优化完成');
  },

  /**
   * 测试图片路径
   */
  testImagePaths: function() {
    console.log('🔍 开始测试图片路径...');

    const imagePaths = [
      '/images/bg.png',
      '/images/boss-normal.png',
      '/images/boss-helmet.png',
      '/images/boss-hit.png',
      '/images/slingshot.png',
      '/images/poop.png',
      '/images/platform.png',
      '/images/power-bg.png',
      '/images/power-fill.png',
      '/images/dot.png'
    ];

    let testResults = '📊 图片路径测试结果:\n\n';
    let successCount = 0;

    imagePaths.forEach((path, index) => {
      // 创建临时image元素测试路径
      const testImage = wx.createOffscreenCanvas().createImage();
      testImage.onload = () => {
        successCount++;
        console.log(`✅ 路径有效: ${path}`);
        testResults += `${index + 1}. ✅ ${path}\n`;

        if (successCount === imagePaths.length) {
          testResults += `\n🎉 所有${imagePaths.length}个图片路径都有效！`;
          wx.showModal({
            title: '🔍 路径测试完成',
            content: testResults,
            showCancel: false
          });
        }
      };

      testImage.onerror = () => {
        console.log(`❌ 路径无效: ${path}`);
        testResults += `${index + 1}. ❌ ${path}\n`;
      };

      testImage.src = path;
    });

    // 显示当前使用的路径
    const currentPaths = `🎮 当前游戏使用的图片路径:

📱 主要素材:
• 背景: /images/bg.png
• Boss: ${this.data.bossImageSrc}
• 弹弓: /images/slingshot.png
• 大粪: /images/poop.png
• 平台: /images/platform.png

🎨 UI素材:
• 力度条背景: /images/power-bg.png
• 力度条填充: /images/power-fill.png
• 轨迹点: /images/dot.png

📂 文件结构:
miniprogram/
├── pages/game-main/ (当前位置)
└── images/ (目标文件夹)

🔧 绝对路径说明:
/images/ = 从小程序根目录访问images文件夹`;

    wx.showModal({
      title: '🔍 图片路径信息',
      content: currentPaths,
      showCancel: false,
      confirmText: '开始测试',
      success: () => {
        wx.showToast({
          title: '正在测试路径...',
          icon: 'loading',
          duration: 2000
        });
      }
    });
  },



  /**
   * 图片加载成功
   */
  onImageLoad: function(e) {
    // 尝试多种方式获取src
    let src = '';
    try {
      src = e.currentTarget.getAttribute('src') ||
            e.currentTarget.src ||
            e.target.src ||
            (e.currentTarget.dataset && e.currentTarget.dataset.src) ||
            'src获取失败';
    } catch (error) {
      src = 'src获取失败';
    }

    console.log('✅ 图片加载成功:', src);
    console.log('   图片尺寸:', e.detail.width, 'x', e.detail.height);

    // 计数已加载的图片
    if (!this.loadedImageCount) {
      this.loadedImageCount = 0;
    }
    this.loadedImageCount++;
    console.log(`📊 已加载图片: ${this.loadedImageCount}/10`);

    // 显示加载成功的Toast
    if (this.loadedImageCount === 1) {
      wx.showToast({
        title: '🎉 图片加载成功！',
        icon: 'success',
        duration: 1500
      });
    }

    // 全部加载完成时显示
    if (this.loadedImageCount >= 6) {
      wx.showToast({
        title: '🚀 素材加载完成！',
        icon: 'success',
        duration: 2000
      });
    }
  },

  /**
   * 图片加载失败
   */
  onImageError: function(e) {
    const srcFromDataset = e.currentTarget.dataset ? e.currentTarget.dataset.src : '';
    const srcFromCurrentTarget = e.currentTarget ? e.currentTarget.src : '';
    const srcFromTarget = e.target ? e.target.src : '';
    const src = srcFromDataset || srcFromCurrentTarget || srcFromTarget || '路径获取失败';

    console.error('❌ 图片加载失败:', e.detail.errMsg);
    console.error('   路径:', src);

    // 显示错误提示
    wx.showToast({
      title: `图片加载失败: ${src}`,
      icon: 'none',
      duration: 3000
    });
  },

  /**
   * 页面卸载
   */
  onUnload: function() {
    if (this.gameLoop) {
      clearTimeout(this.gameLoop);
    }
  }
});