/* game-main.wxss - 丢粪大作战 PRO (美术素材版) */
.container {
  width: 100%;
  height: 100vh;
  background: linear-gradient(180deg, #87CEEB 0%, #4A90E2 50%, #2E5C8F 100%);
  position: relative;
  overflow: hidden;
}

/* 游戏头部 */
.game-header {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 15rpx 20rpx;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10rpx);
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.15);
  z-index: 10;
}

.game-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #8B4513;
  margin-bottom: 5rpx;
  text-shadow: 2rpx 2rpx 4rpx rgba(0, 0, 0, 0.2);
}

.game-subtitle {
  font-size: 24rpx;
  color: #FF6B6B;
  font-style: italic;
  margin-bottom: 10rpx;
}

.game-stats {
  display: flex;
  justify-content: space-around;
  width: 100%;
  gap: 20rpx;
}

.score, .level, .chances {
  font-size: 22rpx;
  color: #333;
  background: rgba(255, 255, 255, 0.8);
  padding: 5rpx 15rpx;
  border-radius: 20rpx;
  border: 2rpx solid #8B4513;
}

/* 游戏场景 */
.game-scene {
  flex: 1;
  position: relative;
  height: calc(100vh - 180rpx);
  overflow: hidden;
  background: #87CEEB;
}

/* 游戏元素定位 */
.background-image {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

.platform-image {
  position: absolute;
  width: 280rpx;
  height: 60rpx;
  z-index: 2;
}

.boss-image {
  position: absolute;
  width: 200rpx;
  height: 240rpx;
  z-index: 3;
  transition: transform 0.3s ease;
}

.slingshot-image {
  position: absolute;
  width: 120rpx;
  height: 160rpx;
  z-index: 4;
}

.poop-image {
  position: absolute;
  width: 48rpx;
  height: 48rpx;
  z-index: 5;
  transition: none;
}

/* 轨迹预判点 */
.trajectory-dot {
  position: absolute;
  width: 12rpx;
  height: 12rpx;
  background: radial-gradient(circle, #FFD700, #FFA500);
  border-radius: 50%;
  z-index: 4;
  pointer-events: none;
}

/* 弹弓皮筋线 */
.slingshot-line {
  position: absolute;
  background: linear-gradient(45deg, #654321, #8B4513);
  height: 6rpx;
  transform-origin: left center;
  z-index: 3;
  border-radius: 3rpx;
  box-shadow: 0 0 4rpx rgba(0,0,0,0.3);
}

/* 调试信息 */
.debug-info {
  position: absolute;
  top: 10rpx;
  left: 10rpx;
  background: rgba(0,0,0,0.7);
  color: white;
  padding: 10rpx;
  border-radius: 8rpx;
  font-size: 20rpx;
  z-index: 10;
}

.debug-info text {
  display: block;
  margin-bottom: 5rpx;
}

/* 力度指示器 - 使用美术素材 */
.power-indicator {
  position: absolute;
  top: 30rpx;
  left: 30rpx;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 15rpx;
  padding: 15rpx 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.3);
  min-width: 200rpx;
  border: 3rpx solid #8B4513;
}

.power-label, .angle-label {
  font-size: 24rpx;
  color: #8B4513;
  font-weight: bold;
  margin-bottom: 8rpx;
  display: block;
  text-shadow: 1rpx 1rpx 2rpx rgba(0, 0, 0, 0.1);
}

.power-bar {
  width: 160rpx;
  height: 40rpx;
  position: relative;
  border-radius: 10rpx;
  overflow: hidden;
}

.power-bar-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

.power-bar-fill {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  z-index: 2;
  transition: width 0.1s ease;
  transform-origin: left center;
}

/* 力度条动画效果 */
.power-fill::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.6), transparent);
  animation: power-shine 2s infinite;
}

@keyframes power-shine {
  0% { left: -100%; }
  100% { left: 100%; }
}

.power-text, .angle-text {
  font-size: 20rpx;
  color: #8B4513;
  text-align: center;
  margin-top: 5rpx;
  font-weight: bold;
  text-shadow: 1rpx 1rpx 2rpx rgba(255, 255, 255, 0.8);
}

/* 角度指示器 */
.angle-indicator {
  position: absolute;
  top: 30rpx;
  right: 30rpx;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 15rpx;
  padding: 15rpx 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.3);
  text-align: center;
  min-width: 120rpx;
  border: 3rpx solid #8B4513;
}

/* 提示文字 */
.hint-text {
  position: absolute;
  bottom: 100rpx;
  left: 50%;
  transform: translateX(-50%);
  background: linear-gradient(45deg, #8B4513, #A0522D);
  color: white;
  padding: 15rpx 25rpx;
  border-radius: 25rpx;
  font-size: 26rpx;
  font-weight: bold;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.4);
  border: 2rpx solid rgba(255, 255, 255, 0.3);
  animation: hint-pulse 2s infinite;
}

@keyframes hint-pulse {
  0%, 100% { 
    opacity: 0.9; 
    transform: translateX(-50%) scale(1);
    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.4);
  }
  50% { 
    opacity: 1; 
    transform: translateX(-50%) scale(1.05);
    box-shadow: 0 6rpx 20rpx rgba(139, 69, 19, 0.6);
  }
}

/* 游戏控制 */
.game-controls {
  position: absolute;
  bottom: 20rpx;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 15rpx;
  z-index: 10;
}

.control-btn {
  height: 70rpx;
  border-radius: 35rpx;
  font-size: 28rpx;
  font-weight: bold;
  border: 2rpx solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 6rpx 12rpx rgba(0, 0, 0, 0.3);
  transition: all 0.3s ease;
  padding: 0 25rpx;
}

.restart-btn {
  background: linear-gradient(45deg, #FF6B6B, #FF8E53);
  color: white;
}

.next-level-btn {
  background: linear-gradient(45deg, #4CAF50, #66BB6A);
  color: white;
}

.debug-btn {
  background: linear-gradient(45deg, #9C27B0, #673AB7);
  color: white;
}

.control-btn:active {
  transform: translateY(2rpx);
  box-shadow: 0 3rpx 6rpx rgba(0, 0, 0, 0.3);
}

/* 弹窗通用样式 */
.result-modal, .level-intro-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background: linear-gradient(135deg, #FFFFFF, #F8F8F8);
  border-radius: 30rpx;
  padding: 60rpx 40rpx;
  margin: 0 60rpx;
  text-align: center;
  box-shadow: 0 20rpx 40rpx rgba(0, 0, 0, 0.4);
  border: 4rpx solid #8B4513;
  animation: modalSlideIn 0.4s ease-out;
  position: relative;
  overflow: hidden;
}

/* 弹窗背景装饰 */
.modal-content::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 6rpx;
  background: linear-gradient(90deg, #FFD700, #FF6B6B, #4CAF50, #2196F3, #9C27B0);
  animation: rainbow-flow 3s infinite;
}

@keyframes rainbow-flow {
  0% { background-position: 0% 50%; }
  100% { background-position: 100% 50%; }
}

.result-icon, .level-icon {
  font-size: 120rpx;
  margin-bottom: 20rpx;
  display: block;
  animation: icon-bounce 1s ease-in-out;
  text-shadow: 4rpx 4rpx 8rpx rgba(0, 0, 0, 0.3);
}

@keyframes icon-bounce {
  0%, 100% { transform: scale(1) rotate(0deg); }
  25% { transform: scale(1.1) rotate(-5deg); }
  75% { transform: scale(1.1) rotate(5deg); }
}

.result-title, .level-title {
  font-size: 48rpx;
  font-weight: bold;
  color: #8B4513;
  margin-bottom: 20rpx;
  display: block;
  text-shadow: 2rpx 2rpx 4rpx rgba(0, 0, 0, 0.2);
}

.result-desc, .level-desc {
  font-size: 32rpx;
  color: #666;
  margin-bottom: 20rpx;
  display: block;
  line-height: 1.5;
}

.score-text {
  font-size: 36rpx;
  font-weight: bold;
  color: #FF6B6B;
  margin-bottom: 30rpx;
  display: block;
  text-shadow: 2rpx 2rpx 4rpx rgba(0, 0, 0, 0.2);
  animation: score-glow 1.5s infinite alternate;
}

@keyframes score-glow {
  0% { text-shadow: 2rpx 2rpx 4rpx rgba(0, 0, 0, 0.2); }
  100% { text-shadow: 2rpx 2rpx 4rpx rgba(255, 107, 107, 0.6), 0 0 10rpx rgba(255, 107, 107, 0.4); }
}

.level-tips {
  text-align: left;
  margin: 20rpx 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(248, 248, 248, 0.8));
  padding: 20rpx;
  border-radius: 15rpx;
  border: 2rpx solid #E0E0E0;
  box-shadow: inset 0 2rpx 4rpx rgba(0, 0, 0, 0.05);
}

.tip-item {
  display: block;
  font-size: 28rpx;
  color: #555;
  margin-bottom: 8rpx;
  line-height: 1.4;
  position: relative;
  padding-left: 20rpx;
}

.tip-item::before {
  content: '💡';
  position: absolute;
  left: 0;
  top: 0;
  font-size: 24rpx;
}

.modal-buttons {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
  margin-top: 30rpx;
}

.modal-btn {
  width: 100%;
  height: 80rpx;
  border-radius: 40rpx;
  font-size: 32rpx;
  font-weight: bold;
  border: 2rpx solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.3);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.lottery-btn {
  background: linear-gradient(45deg, #FFD700, #FFC107);
  color: #8B4513;
}

.retry-btn {
  background: linear-gradient(45deg, #4CAF50, #66BB6A);
  color: white;
}

.ad-btn {
  background: linear-gradient(45deg, #9C27B0, #BA68C8);
  color: white;
}

.restart-btn {
  background: linear-gradient(45deg, #2196F3, #42A5F5);
  color: white;
}

.start-btn {
  background: linear-gradient(45deg, #FF6B6B, #FF8E53);
  color: white;
}

/* 按钮悬停特效 */
.modal-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.5s ease;
}

.modal-btn:active {
  transform: translateY(2rpx);
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.3);
}

.modal-btn:active::before {
  left: 100%;
}

/* 动画 */
@keyframes modalSlideIn {
  0% {
    opacity: 0;
    transform: translateY(-100rpx) scale(0.8);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Canvas边框美化 */
.game-canvas, .game-canvas-backup {
  border: 3rpx solid rgba(139, 69, 19, 0.3);
  box-shadow: inset 0 0 20rpx rgba(0, 0, 0, 0.1);
}

/* 响应式适配 */
@media (max-height: 1000rpx) {
  .game-header {
    padding: 10rpx 15rpx;
  }
  
  .game-title {
    font-size: 32rpx;
  }
  
  .power-indicator, .angle-indicator {
    top: 20rpx;
  }
  
  .game-controls {
    bottom: 15rpx;
  }
  
  .modal-content {
    padding: 40rpx 30rpx;
    margin: 0 40rpx;
  }
} 

/* 加载状态提示 */
.loading-indicator {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgba(255, 255, 255, 0.95);
  padding: 40rpx 60rpx;
  border-radius: 30rpx;
  box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.3);
  text-align: center;
  z-index: 50;
  border: 3rpx solid #8B4513;
  backdrop-filter: blur(10rpx);
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 6rpx solid #E0E0E0;
  border-top: 6rpx solid #8B4513;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 28rpx;
  color: #8B4513;
  margin-top: 20rpx;
  font-weight: bold;
  display: block;
} 