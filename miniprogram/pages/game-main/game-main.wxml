<!--game-main.wxml - 丢粪大作战 PRO-->
<view class="container">
  
  <!-- 游戏顶部信息栏 -->
  <view class="game-header">
    <view class="game-title">丢粪大作战 PRO</view>
    <view class="game-subtitle">发射的不是大粪，是怒火！</view>
    <view class="game-stats">
      <text class="score">得分: {{score}}</text>
      <text class="level">关卡: {{currentLevel}}</text>
      <text class="chances">机会: {{chancesLeft}}/3</text>
    </view>
  </view>

  <!-- 游戏主画面 -->
  <view class="game-scene" 
        bindtouchstart="onTouchStart"
        bindtouchmove="onTouchMove" 
        bindtouchend="onTouchEnd">
    
    <!-- 厕所背景 -->
    <image class="background-image" src="../../images/bg.png" mode="scaleToFill"
           binderror="onImageError" bindload="onImageLoad"></image>
    

    
    <!-- 台子平台 -->
    <image class="platform-image" 
           src="../../images/platform.png" 
           style="left: {{platformX}}rpx; top: {{platformY}}rpx;"
           mode="scaleToFill"
           binderror="onImageError"
           bindload="onImageLoad"></image>
    
    <!-- Boss角色 -->
    <image class="boss-image" 
           src="{{bossImageSrc}}" 
           style="left: {{bossX}}rpx; top: {{bossY}}rpx; transform: translateX({{bossOffsetX}}rpx);"
           mode="scaleToFill"
           binderror="onImageError"
           bindload="onImageLoad"></image>
    
    <!-- 弹弓发射器 -->
    <image class="slingshot-image" 
           src="../../images/slingshot.png"
           style="left: {{slingshotX}}rpx; top: {{slingshotY}}rpx;"
           mode="scaleToFill"
           binderror="onImageError"
           bindload="onImageLoad"></image>
    
    <!-- 大粪投射物 -->
    <image class="poop-image" 
           src="../../images/poop.png"
           style="left: {{poopX}}rpx; top: {{poopY}}rpx; opacity: {{poopVisible ? 1 : 0}};"
           mode="scaleToFill"
           binderror="onImageError"
           bindload="onImageLoad"></image>
    
    <!-- 拖拽时的皮筋线 -->
    <view class="slingshot-line" wx:if="{{isDragging}}" 
          style="{{slingshotLineStyle}}"></view>
    
    <!-- 轨迹预判点 -->
    <view wx:for="{{trajectoryPoints}}" wx:key="index" class="trajectory-dot"
          style="left: {{item.x}}px; top: {{item.y}}px; opacity: {{item.opacity}};"></view>

    <!-- 力度指示器 -->
    <view class="power-indicator" wx:if="{{isDragging}}">
      <view class="power-label">力度</view>
      <view class="power-bar">
        <image class="power-bar-bg" src="../../images/power-bg.png" mode="scaleToFill"></image>
        <image class="power-bar-fill" src="../../images/power-fill.png" 
               style="width: {{power}}%;" mode="scaleToFill"></image>
      </view>
      <view class="power-text">{{Math.round(power)}}%</view>
    </view>

    <!-- 角度指示器 -->
    <view class="angle-indicator" wx:if="{{isDragging}}">
      <view class="angle-label">角度</view>
      <view class="angle-text">{{Math.round(angle)}}°</view>
    </view>

    <!-- 提示文字 -->
    <view class="hint-text" wx:if="{{!isGameStarted}}">
      拖拽大粪调整角度和力度，松手发射！
    </view>
    
    <!-- 游戏状态调试 -->
    <view class="debug-info" wx:if="{{showDebug}}">
      <text>Boss: ({{bossX}}, {{bossY}})</text>
      <text>大粪: ({{poopX}}, {{poopY}})</text>
      <text>飞行中: {{isFlying ? '是' : '否'}}</text>
    </view>
  </view>

  <!-- 游戏控制区域 -->
  <view class="game-controls">
    <button class="control-btn restart-btn" bindtap="restartLevel" wx:if="{{isGameStarted}}">
      重新开始
    </button>
    <button class="control-btn next-level-btn" bindtap="nextLevel" wx:if="{{levelCompleted}}">
      下一关
    </button>
    <button class="control-btn start-btn" bindtap="forceShowLevelIntro" wx:if="{{!showLevelIntro && !isGameStarted}}">
      显示关卡介绍
    </button>
    <button class="control-btn debug-btn" bindtap="showDebugInfo">
      调试信息
    </button>
    <button class="control-btn debug-btn" bindtap="checkLayout">
      检查布局
    </button>
    <button class="control-btn debug-btn" bindtap="testImagePaths">
      测试图片路径
    </button>
  </view>

  <!-- 游戏结果弹窗 -->
  <view class="result-modal" wx:if="{{showResultModal}}">
    <view class="modal-content">
      <view class="result-icon">{{resultType === 'hit' ? '🎯' : '😅'}}</view>
      <text class="result-title">{{resultType === 'hit' ? '命中目标！' : '差点就中！'}}</text>
      <text class="result-desc">{{resultMessage}}</text>
      <text class="score-text" wx:if="{{resultType === 'hit'}}">得分 +{{lastScore}}</text>
      
      <view class="modal-buttons">
        <button class="modal-btn lottery-btn" bindtap="goToLottery" wx:if="{{resultType === 'hit'}}">
          领取奖励
        </button>
        <button class="modal-btn retry-btn" bindtap="retryShot" wx:if="{{chancesLeft > 0}}">
          再试一次 ({{chancesLeft}}/3)
        </button>
        <button class="modal-btn ad-btn" bindtap="watchAdForChance" wx:if="{{chancesLeft === 0}}">
          看广告获得机会
        </button>
        <button class="modal-btn restart-btn" bindtap="restartLevel">
          重新开始关卡
        </button>
      </view>
    </view>
  </view>

  <!-- 关卡介绍弹窗 -->
  <view class="level-intro-modal" wx:if="{{showLevelIntro}}">
    <view class="modal-content">
      <view class="level-icon">{{levelConfig.icon || '🎯'}}</view>
      <text class="level-title">{{levelConfig.title || '第1关 - 新手挑战'}}</text>
      <text class="level-desc">{{levelConfig.description || '瞄准云纸老板，试试手感！'}}</text>
      <view class="level-tips">
        <text class="tip-item" wx:for="{{levelConfig.tips || ['拖拽大粪调整角度和力度', '松手发射', '击中目标获得奖励']}}" wx:key="index">• {{item}}</text>
      </view>
      <view class="modal-buttons">
        <button class="modal-btn start-btn" bindtap="startLevel">
          开始挑战
        </button>
      </view>
    </view>
  </view>
  


</view> 