<!--lottery.wxml-->
<view class="container">
  
  <!-- 标题区域 -->
  <view class="header">
    <text class="title">奖励中心</text>
    <view class="sparkles">✨</view>
  </view>

  <!-- 抽奖转盘 -->
  <view class="lottery-area">
    <view class="wheel-container">
      <!-- 转盘背景 -->
      <view class="wheel" style="transform: rotate({{wheelRotation}}deg)">
        
        <!-- 奖励扇形区域 -->
        <view wx:for="{{prizes}}" wx:key="index" 
              class="prize-sector" 
              style="transform: rotate({{item.rotation}}deg)">
          <view class="prize-content">
            <view class="prize-icon">{{item.icon}}</view>
            <text class="prize-text">{{item.name}}</text>
          </view>
        </view>
        
      </view>
      
      <!-- 指针 -->
      <view class="pointer"></view>
      
      <!-- 中心按钮 -->
      <button class="spin-button" 
              bindtap="spinWheel" 
              disabled="{{isSpinning}}"
              hover-class="spin-button-hover">
        <text class="spin-text">{{isSpinning ? '抽奖中' : '开始抽奖'}}</text>
      </button>
    </view>
  </view>

  <!-- 抽奖结果弹窗 -->
  <view class="result-modal" wx:if="{{showResult}}">
    <view class="modal-content">
      <view class="result-icon">{{selectedPrize.icon}}</view>
      <text class="result-title">恭喜获得</text>
      <text class="result-prize">{{selectedPrize.name}}</text>
      <text class="result-desc">{{selectedPrize.description}}</text>
      
      <view class="modal-buttons">
        <button class="claim-btn" bindtap="claimPrize">立即领取</button>
        <button class="continue-btn" bindtap="continuePlaying">继续游戏</button>
      </view>
    </view>
  </view>

  <!-- 我的奖励 -->
  <view class="my-rewards">
    <view class="section-title">
      <text class="title-text">我的奖励</text>
      <view class="rewards-count">{{myRewards.length}}个</view>
    </view>
    
    <scroll-view class="rewards-list" scroll-x="true">
      <view wx:for="{{myRewards}}" wx:key="index" class="reward-item">
        <view class="reward-icon">{{item.icon}}</view>
        <text class="reward-name">{{item.name}}</text>
        <text class="reward-status">{{item.status}}</text>
      </view>
      
      <!-- 空状态 -->
      <view wx:if="{{myRewards.length === 0}}" class="empty-rewards">
        <text class="empty-text">还没有奖励哦~</text>
        <text class="empty-hint">快去游戏中获得奖励吧！</text>
      </view>
    </scroll-view>
  </view>

  <!-- 抽奖规则 -->
  <view class="rules-section">
    <view class="rules-toggle" bindtap="toggleRules">
      <text class="rules-title">抽奖规则</text>
      <text class="toggle-icon">{{showRules ? '▼' : '▶'}}</text>
    </view>
    
    <view wx:if="{{showRules}}" class="rules-content">
      <text class="rule-item">• 击中目标后可获得抽奖机会</text>
      <text class="rule-item">• 每个奖励都有不同的获奖概率</text>
      <text class="rule-item">• 红包奖励可直接提现使用</text>
      <text class="rule-item">• 退费券仅限下次购买使用</text>
    </view>
  </view>

</view> 