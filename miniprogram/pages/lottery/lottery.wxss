/* lottery.wxss */
.container {
  width: 100%;
  height: 100vh;
  background: linear-gradient(180deg, #FFE4E1 0%, #FFA07A 100%);
  position: relative;
  overflow: hidden;
}

/* 标题区域 */
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 30rpx;
  background: rgba(255,255,255,0.9);
  backdrop-filter: blur(10rpx);
  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.1);
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.back-btn {
  width: 60rpx;
  height: 60rpx;
  border-radius: 30rpx;
  background: #FF6B6B;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
}

/* 抽奖区域 */
.lottery-section {
  padding: 40rpx 30rpx;
  text-align: center;
}

.wheel-container {
  position: relative;
  width: 600rpx;
  height: 600rpx;
  margin: 0 auto 40rpx;
}

.wheel {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background: conic-gradient(from 0deg, #FF6B6B 0deg 60deg, #4CAF50 60deg 120deg, #2196F3 120deg 180deg, #FFD700 180deg 240deg, #9C27B0 240deg 300deg, #FF9800 300deg 360deg);
  position: relative;
  transition: transform 3s cubic-bezier(0.25,0.46,0.45,0.94);
  box-shadow: 0 0 30rpx rgba(0,0,0,0.3);
}

.wheel-item {
  position: absolute;
  width: 50%;
  height: 50%;
  transform-origin: 100% 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  color: white;
  font-weight: bold;
}

.wheel-item:nth-child(1) { transform: rotate(0deg); }
.wheel-item:nth-child(2) { transform: rotate(60deg); }
.wheel-item:nth-child(3) { transform: rotate(120deg); }
.wheel-item:nth-child(4) { transform: rotate(180deg); }
.wheel-item:nth-child(5) { transform: rotate(240deg); }
.wheel-item:nth-child(6) { transform: rotate(300deg); }

.prize-icon {
  font-size: 48rpx;
  margin-bottom: 10rpx;
}

.prize-name {
  font-size: 24rpx;
}

/* 指针 */
.pointer {
  position: absolute;
  top: -20rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 0;
  border-left: 20rpx solid transparent;
  border-right: 20rpx solid transparent;
  border-bottom: 40rpx solid #333;
  z-index: 10;
}

/* 中心按钮 */
.spin-btn {
  width: 300rpx;
  height: 80rpx;
  border-radius: 40rpx;
  background: linear-gradient(45deg, #FF6B6B, #FF8E53);
  color: white;
  font-size: 32rpx;
  font-weight: bold;
  border: none;
  box-shadow: 0 8rpx 16rpx rgba(0,0,0,0.2);
  margin-bottom: 30rpx;
}

.spin-btn:disabled {
  background: #CCCCCC;
  color: #999;
  box-shadow: none;
}

.rules-btn {
  background: transparent;
  color: #666;
  font-size: 28rpx;
  border: 2rpx solid #DDD;
  padding: 10rpx 30rpx;
  border-radius: 20rpx;
}

/* 结果弹窗 */
.rewards-section {
  padding: 0 30rpx 40rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 30rpx;
  text-align: center;
}

.reward-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.reward-item {
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
  display: flex;
  align-items: center;
  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.1);
}

.reward-icon {
  font-size: 48rpx;
  margin-right: 20rpx;
}

.reward-info {
  flex: 1;
}

.reward-name {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 5rpx;
}

.reward-status {
  font-size: 24rpx;
  color: #666;
}

.empty-rewards {
  text-align: center;
  color: #999;
  font-size: 28rpx;
  padding: 60rpx 0;
}

.modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0,0,0,0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  border-radius: 30rpx;
  padding: 60rpx 40rpx;
  margin: 0 60rpx;
  text-align: center;
  box-shadow: 0 20rpx 40rpx rgba(0,0,0,0.3);
  animation: modalSlideIn 0.3s ease-out;
}

.modal-icon {
  font-size: 100rpx;
  margin-bottom: 20rpx;
}

.modal-title {
  font-size: 40rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.modal-desc {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 40rpx;
}

.modal-buttons {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.modal-btn {
  width: 100%;
  height: 80rpx;
  border-radius: 40rpx;
  font-size: 32rpx;
  font-weight: bold;
  border: none;
  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.2);
}

.claim-btn {
  background: linear-gradient(45deg, #4CAF50, #66BB6A);
  color: white;
}

.continue-btn {
  background: linear-gradient(45deg, #2196F3, #42A5F5);
  color: white;
}

.rules-modal .modal-content {
  text-align: left;
}

.rules-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 30rpx;
  text-align: center;
}

.rules-list {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
}

.rules-item {
  margin-bottom: 20rpx;
}

.close-btn {
  background: linear-gradient(45deg, #757575, #9E9E9E);
  color: white;
}

/* 动画 */
@keyframes modalSlideIn {
  0% {
    opacity: 0;
    transform: translateY(-100rpx) scale(0.8);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
} 