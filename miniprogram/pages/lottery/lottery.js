// pages/lottery/lottery.js

Page({
  /**
   * 页面的初始数据
   */
  data: {
    wheelRotation: 0,
    isSpinning: false,
    showResult: false,
    showRules: false,
    selectedPrize: {},
    myRewards: [],
    
    // 奖品配置
    prizes: [
      { 
        id: 1, 
        name: '1元红包', 
        icon: '🧧', 
        description: '可直接提现到微信钱包',
        probability: 30,
        rotation: 0 
      },
      { 
        id: 2, 
        name: '退费券', 
        icon: '🎫', 
        description: '下次购买纸巾可抵扣',
        probability: 20,
        rotation: 60 
      },
      { 
        id: 3, 
        name: '5元红包', 
        icon: '💰', 
        description: '可直接提现到微信钱包',
        probability: 10,
        rotation: 120 
      },
      { 
        id: 4, 
        name: '再来一次', 
        icon: '🎁', 
        description: '获得额外投掷机会',
        probability: 25,
        rotation: 180 
      },
      { 
        id: 5, 
        name: '谢谢参与', 
        icon: '😊', 
        description: '很遗憾，下次再来',
        probability: 10,
        rotation: 240 
      },
      { 
        id: 6, 
        name: '2倍积分', 
        icon: '⭐', 
        description: '下局游戏积分翻倍',
        probability: 5,
        rotation: 300 
      }
    ]
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function() {
    console.log('抽奖页面加载');
    this.loadUserRewards();
  },

  /**
   * 加载用户奖励
   */
  loadUserRewards: function() {
    try {
      var rewards = wx.getStorageSync('userRewards') || [];
      this.setData({ myRewards: rewards });
      console.log('已加载奖励记录:', rewards.length);
    } catch (error) {
      console.error('加载用户奖励失败:', error);
    }
  },

  /**
   * 开始转盘抽奖
   */
  spinWheel: function() {
    if (this.data.isSpinning) return;
    
    console.log('开始转盘抽奖');
    
    this.setData({ isSpinning: true });
    
    // 播放旋转音效
    wx.vibrateShort({ type: 'heavy' });
    
    // 根据概率选择奖品
    var selectedPrize = this.selectPrizeByProbability();
    console.log('选中奖品:', selectedPrize.name);
    
    // 计算转盘应该停止的角度
    var targetAngle = this.calculateTargetAngle(selectedPrize);
    
    // 执行旋转动画（多转几圈增加悬念）
    var finalRotation = this.data.wheelRotation + 1440 + targetAngle; // 4圈 + 目标角度
    
    this.setData({ 
      wheelRotation: finalRotation,
      selectedPrize: selectedPrize 
    });
    
    // 3秒后显示结果
    var that = this;
    setTimeout(function() {
      that.setData({ 
        isSpinning: false,
        showResult: true 
      });
      
      // 播放获奖音效
      wx.vibrateShort({ type: 'heavy' });
    }, 3000);
  },

  /**
   * 根据概率选择奖品
   */
  selectPrizeByProbability: function() {
    var random = Math.random() * 100;
    var currentProbability = 0;
    
    for (var i = 0; i < this.data.prizes.length; i++) {
      var prize = this.data.prizes[i];
      currentProbability += prize.probability;
      if (random <= currentProbability) {
        return prize;
      }
    }
    
    // 默认返回最后一个奖品
    return this.data.prizes[this.data.prizes.length - 1];
  },

  /**
   * 计算目标角度
   */
  calculateTargetAngle: function(prize) {
    // 指针指向12点方向，需要计算奖品扇形的中心角度
    var sectorAngle = 360 / this.data.prizes.length;
    var prizeIndex = this.data.prizes.findIndex(function(p) { return p.id === prize.id; });
    
    // 计算该奖品扇形的中心角度
    var centerAngle = prizeIndex * sectorAngle + sectorAngle / 2;
    
    // 转换为指针应该指向的角度（360度减去奖品角度）
    return 360 - centerAngle;
  },

  /**
   * 领取奖励
   */
  claimPrize: function() {
    var prize = this.data.selectedPrize;
    console.log('领取奖励:', prize.name);
    
    // 添加到用户奖励列表
    var newReward = {
      id: Date.now(),
      name: prize.name,
      icon: prize.icon,
      status: '已领取',
      claimTime: new Date().toLocaleString()
    };
    
    var updatedRewards = this.data.myRewards.concat([newReward]);
    this.setData({ myRewards: updatedRewards });
    
    // 保存到本地存储
    try {
      wx.setStorageSync('userRewards', updatedRewards);
      console.log('奖励已保存到本地');
    } catch (error) {
      console.error('保存奖励失败:', error);
    }
    
    // 根据奖品类型执行不同操作
    this.handlePrizeAction(prize);
    
    this.setData({ showResult: false });
    
    wx.showToast({
      title: '已领取 ' + prize.name,
      icon: 'success'
    });
  },

  /**
   * 处理奖品动作
   */
  handlePrizeAction: function(prize) {
    switch (prize.id) {
      case 1: // 1元红包
      case 3: // 5元红包
        this.showWithdrawModal(prize);
        break;
      case 2: // 退费券
        this.showCouponDetails(prize);
        break;
      case 4: // 再来一次
        this.grantExtraChance();
        break;
      case 6: // 2倍积分
        this.grantDoubleScore();
        break;
      default:
        // 谢谢参与，无特殊动作
        break;
    }
  },

  /**
   * 显示提现模态框
   */
  showWithdrawModal: function(prize) {
    wx.showModal({
      title: '红包提现',
      content: '恭喜获得' + prize.name + '！是否立即提现到微信钱包？',
      confirmText: '立即提现',
      cancelText: '稍后提现',
      success: function(res) {
        if (res.confirm) {
          // 这里应该调用真实的提现接口
          wx.showToast({
            title: '提现申请已提交',
            icon: 'success'
          });
        }
      }
    });
  },

  /**
   * 显示优惠券详情
   */
  showCouponDetails: function(prize) {
    wx.showModal({
      title: '优惠券',
      content: prize.description + '\n\n有效期：30天\n使用范围：全场商品',
      showCancel: false,
      confirmText: '我知道了'
    });
  },

  /**
   * 给予额外机会
   */
  grantExtraChance: function() {
    wx.showModal({
      title: '额外机会',
      content: '恭喜获得3次额外投掷机会！是否立即使用？',
      confirmText: '立即使用',
      cancelText: '稍后使用',
      success: function(res) {
        if (res.confirm) {
          wx.navigateTo({
            url: '/pages/game-main/game-main'
          });
        }
      }
    });
  },

  /**
   * 给予双倍积分
   */
  grantDoubleScore: function() {
    try {
      wx.setStorageSync('doubleScoreEnabled', true);
      wx.showToast({
        title: '下局游戏积分翻倍',
        icon: 'success'
      });
    } catch (error) {
      console.error('设置双倍积分失败:', error);
    }
  },

  /**
   * 继续游戏
   */
  continuePlaying: function() {
    this.setData({ showResult: false });
    wx.navigateTo({
      url: '/pages/game-main/game-main'
    });
  },

  /**
   * 切换规则显示
   */
  toggleRules: function() {
    this.setData({ showRules: !this.data.showRules });
  },

  /**
   * 查看奖励详情
   */
  viewRewardDetail: function(e) {
    var index = e.currentTarget.dataset.index;
    var reward = this.data.myRewards[index];
    
    wx.showModal({
      title: reward.name,
      content: '状态：' + reward.status + '\n领取时间：' + (reward.claimTime || '未领取'),
      showCancel: false,
      confirmText: '确定'
    });
  },

  /**
   * 分享页面
   */
  onShareAppMessage: function() {
    return {
      title: '我在丢粪大作战中抽到了好礼！',
      path: '/pages/lottery/lottery',
      imageUrl: '' // 可以设置分享图片
    };
  },

  /**
   * 返回上一页
   */
  goBack: function() {
    wx.navigateBack();
  },

  /**
   * 页面显示时刷新奖励列表
   */
  onShow: function() {
    console.log('抽奖页面显示');
    this.loadUserRewards();
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function() {
    console.log('抽奖页面渲染完成');
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function() {
    console.log('抽奖页面隐藏');
  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function() {
    console.log('抽奖页面卸载');
  }
});