/* game-intro.wxss */
.container {
  height: 100vh;
  background: linear-gradient(180deg, #F0F8FF 0%, #E6F3FF 100%);
}

.header {
  background: linear-gradient(135deg, #4A90E2, #5BA3F5);
  padding: 60rpx 0 40rpx 0;
  text-align: center;
  box-shadow: 0 4rpx 12rpx rgba(74, 144, 226, 0.3);
}

.title {
  font-size: 48rpx;
  font-weight: bold;
  color: white;
  text-shadow: 2rpx 2rpx 4rpx rgba(0,0,0,0.2);
}

.content {
  padding: 40rpx 30rpx 150rpx 30rpx;
}

.section {
  background: white;
  border-radius: 20rpx;
  margin-bottom: 30rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.1);
}

.section-title {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.step-number {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60rpx;
  height: 60rpx;
  background: linear-gradient(135deg, #FF6B6B, #FF8E53);
  color: white;
  border-radius: 50%;
  font-size: 28rpx;
  font-weight: bold;
  margin-right: 20rpx;
}

.step-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.section-content {
  padding-left: 80rpx;
}

.instruction-item {
  display: flex;
  align-items: center;
  padding: 10rpx 0;
}

.icon {
  font-size: 48rpx;
  margin-right: 20rpx;
}

.text {
  font-size: 32rpx;
  color: #666;
  line-height: 1.5;
}

.reward-section {
  background: linear-gradient(135deg, #FFE082, #FFCC02);
  border-radius: 20rpx;
  padding: 30rpx;
  margin: 30rpx 0;
  box-shadow: 0 4rpx 12rpx rgba(255, 204, 2, 0.3);
}

.reward-title {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  color: #8B4513;
  text-align: center;
  margin-bottom: 30rpx;
}

.reward-grid {
  display: flex;
  justify-content: space-around;
}

.reward-item {
  text-align: center;
  flex: 1;
}

.reward-icon {
  font-size: 60rpx;
  margin-bottom: 10rpx;
}

.reward-text {
  display: block;
  font-size: 28rpx;
  color: #8B4513;
  font-weight: 500;
}

.ad-section {
  background: linear-gradient(135deg, #E8F5E8, #C8E6C9);
  border-radius: 20rpx;
  padding: 30rpx;
  margin: 30rpx 0;
  border: 2rpx dashed #4CAF50;
}

.ad-content {
  display: flex;
  align-items: center;
}

.ad-icon {
  font-size: 60rpx;
  margin-right: 20rpx;
}

.ad-text {
  flex: 1;
}

.ad-title {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: #2E7D32;
  margin-bottom: 8rpx;
}

.ad-desc {
  display: block;
  font-size: 28rpx;
  color: #4CAF50;
}

.footer {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  padding: 30rpx;
  box-shadow: 0 -4rpx 12rpx rgba(0,0,0,0.1);
}

.start-game-btn {
  width: 100%;
  height: 100rpx;
  background: linear-gradient(135deg, #4A90E2, #357ABD);
  color: white;
  border: none;
  border-radius: 50rpx;
  font-size: 36rpx;
  font-weight: bold;
  box-shadow: 0 8rpx 16rpx rgba(74, 144, 226, 0.4);
}

.start-game-btn:active {
  transform: translateY(2rpx);
  box-shadow: 0 4rpx 8rpx rgba(74, 144, 226, 0.4);
} 