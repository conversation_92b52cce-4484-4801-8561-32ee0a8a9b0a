// pages/game-intro/game-intro.js

Page({
  /**
   * 页面的初始数据
   */
  data: {

  },

  /**
   * 开始游戏
   */
  startGame: function() {
    console.log('从介绍页开始游戏');
    
    wx.vibrateShort({
      type: 'medium'
    });
    
    // 跳转到游戏主界面
    wx.navigateTo({
      url: '/pages/game-main/game-main',
      success: function() {
        console.log('成功跳转到游戏主界面');
      },
      fail: function(err) {
        console.error('跳转失败:', err);
        wx.showToast({
          title: '跳转失败',
          icon: 'error'
        });
      }
    });
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function() {
    console.log('游戏介绍页面加载');
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function() {
    console.log('游戏介绍页面渲染完成');
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function() {
    console.log('游戏介绍页面显示');
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function() {
    console.log('游戏介绍页面隐藏');
  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function() {
    console.log('游戏介绍页面卸载');
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function() {
    console.log('下拉刷新');
    wx.stopPullDownRefresh && wx.stopPullDownRefresh();
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function() {
    console.log('上拉触底');
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function() {
    return {
      title: '丢粪大作战 - 游戏说明',
      path: '/pages/game-intro/game-intro'
    };
  }
});