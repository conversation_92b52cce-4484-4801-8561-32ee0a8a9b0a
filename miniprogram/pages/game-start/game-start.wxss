/* game-start.wxss */
.container {
  width: 100%;
  height: 100vh;
  position: relative;
  overflow: hidden;
}

.background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(180deg, #87CEEB 0%, #4A90E2 100%);
  z-index: -1;
}

/* 游戏标题 */
.title {
  text-align: center;
  padding-top: 100rpx;
  margin-bottom: 60rpx;
}

.main-title {
  display: block;
  font-size: 72rpx;
  font-weight: bold;
  color: #FFD700;
  text-shadow: 4rpx 4rpx 8rpx rgba(0,0,0,0.3);
  margin-bottom: 20rpx;
  letter-spacing: 6rpx;
}

.sub-title {
  display: block;
  font-size: 32rpx;
  color: #FFFFFF;
  font-weight: 500;
}

/* 角色展示区 */
.character-area {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400rpx;
  position: relative;
  margin: 80rpx 0;
}

.toilet-paper-character {
  position: relative;
  z-index: 2;
}

.paper-body {
  width: 160rpx;
  height: 200rpx;
  background: #FFFFFF;
  border-radius: 20rpx;
  border: 6rpx solid #8B4513;
  position: relative;
  box-shadow: 0 8rpx 16rpx rgba(0,0,0,0.2);
  animation: bounce 2s infinite ease-in-out;
}

.paper-face {
  position: absolute;
  top: 60rpx;
  left: 50%;
  transform: translateX(-50%);
}

.eye {
  width: 20rpx;
  height: 20rpx;
  background: #000;
  border-radius: 50%;
  display: inline-block;
  margin: 0 8rpx;
}

.mouth {
  width: 30rpx;
  height: 15rpx;
  background: #FF6B6B;
  border-radius: 0 0 30rpx 30rpx;
  margin: 10rpx auto;
}

.bow-tie {
  position: absolute;
  bottom: 40rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 40rpx;
  height: 25rpx;
  background: #8B0000;
  border-radius: 8rpx;
}

/* 漂浮粪便 */
.poop {
  position: absolute;
  font-size: 60rpx;
  opacity: 0.7;
}

.floating-poop-1 {
  top: 50rpx;
  left: 100rpx;
  animation: float1 3s infinite ease-in-out;
}

.floating-poop-2 {
  top: 200rpx;
  right: 100rpx;
  animation: float2 4s infinite ease-in-out;
}

/* 按钮区域 */
.button-area {
  text-align: center;
  margin-top: 100rpx;
}

.start-btn {
  width: 500rpx;
  height: 100rpx;
  background: linear-gradient(45deg, #FF6B6B, #FF8E53);
  color: white;
  border: none;
  border-radius: 50rpx;
  font-size: 36rpx;
  font-weight: bold;
  box-shadow: 0 8rpx 16rpx rgba(255,107,107,0.4);
  margin-bottom: 30rpx;
}

.start-btn:active {
  transform: translateY(2rpx);
  box-shadow: 0 4rpx 8rpx rgba(255,107,107,0.4);
}

.slogan {
  display: block;
  font-size: 28rpx;
  color: #FFFFFF;
  margin-top: 20rpx;
}

/* 底部信息 */
.bottom-info {
  position: absolute;
  bottom: 60rpx;
  left: 50%;
  transform: translateX(-50%);
}

.info-btn {
  background: rgba(255,255,255,0.2);
  color: white;
  border: 2rpx solid rgba(255,255,255,0.5);
  border-radius: 40rpx;
  padding: 20rpx 40rpx;
  font-size: 28rpx;
}

/* 动画 */
@keyframes bounce {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-20rpx);
  }
}

@keyframes float1 {
  0%, 100% {
    transform: translateY(0) rotate(0deg);
  }
  50% {
    transform: translateY(-30rpx) rotate(180deg);
  }
}

@keyframes float2 {
  0%, 100% {
    transform: translateY(0) rotate(0deg);
  }
  33% {
    transform: translateY(-20rpx) rotate(120deg);
  }
  66% {
    transform: translateY(-40rpx) rotate(240deg);
  }
} 