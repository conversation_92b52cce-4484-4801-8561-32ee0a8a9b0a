// pages/game-start/game-start.js

Page({
  /**
   * 页面的初始数据
   */
  data: {
    
  },

  /**
   * 开始游戏
   */
  startGame: function() {
    console.log('开始游戏按钮被点击');
    
    wx.showToast({
      title: '正在进入游戏...',
      icon: 'loading',
      duration: 1000
    });
    
    // 震动反馈
    try {
      wx.vibrateShort({
        type: 'medium'
      });
    } catch (e) {
      console.log('震动API不支持:', e);
    }
    
    // 跳转到游戏主界面
    setTimeout(function() {
      wx.navigateTo({
        url: '/pages/game-main/game-main',
        success: function() {
          console.log('成功跳转到游戏主界面');
        },
        fail: function(err) {
          console.error('跳转失败:', err);
          wx.showToast({
            title: '跳转失败',
            icon: 'error'
          });
        }
      });
    }, 500);
  },

  /**
   * 显示游戏说明
   */
  showGameIntro: function() {
    console.log('游戏说明按钮被点击');
    
    wx.showToast({
      title: '正在加载说明...',
      icon: 'loading',
      duration: 1000
    });
    
    // 震动反馈
    try {
      wx.vibrateShort({
        type: 'light'
      });
    } catch (e) {
      console.log('震动API不支持:', e);
    }
    
    // 跳转到游戏介绍页
    setTimeout(function() {
      wx.navigateTo({
        url: '/pages/game-intro/game-intro',
        success: function() {
          console.log('成功跳转到游戏介绍页');
        },
        fail: function(err) {
          console.error('跳转失败:', err);
          wx.showToast({
            title: '跳转失败',
            icon: 'error'
          });
        }
      });
    }, 500);
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function(options) {
    console.log('游戏开始页面加载', options);
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function() {
    console.log('游戏开始页面渲染完成');
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function() {
    console.log('游戏开始页面显示');
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function() {
    console.log('游戏开始页面隐藏');
  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function() {
    console.log('游戏开始页面卸载');
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function() {
    console.log('下拉刷新');
    if (wx.stopPullDownRefresh) {
      wx.stopPullDownRefresh();
    }
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function() {
    console.log('上拉触底');
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function() {
    return {
      title: '丢粪大作战 - 扔得准，纸钱退！',
      path: '/pages/game-start/game-start',
      imageUrl: ''
    };
  }
});