# 🎨 游戏素材文件名对照表

## 📋 完整对照关系

| 中文原名 | 英文文件名 | 文件大小 | 用途说明 |
|---------|-----------|---------|----------|
| 厕所背景图.png | bg.png | 2.5MB | 游戏主背景场景 |
| 弹弓发射器.png | slingshot.png | 2.1MB | 底部弹弓发射装置 |
| 云纸老板-正常状态.png | boss-normal.png | 1.4MB | Boss默认状态 |
| 云纸老板-戴安全帽.png | boss-helmet.png | 1.1MB | Boss防护状态 |
| 云纸老板-被击中状态.png | boss-hit.png | 951KB | Boss被击中反馈 |
| 大粪素材.png | poop.png | 494KB | 投射物弹药 |
| 台子平台.png | platform.png | 514KB | Boss站立台子 |
| 力度条背景.png | power-bg.png | 303KB | 力度条UI背景 |
| 力度条填充.png | power-fill.png | 287KB | 力度条UI填充 |
| 轨迹点素材.png | dot.png | 95KB | 轨迹预判点 |

## 🔧 使用说明

### 在WXML中使用
```xml
<!-- 背景图 -->
<image class="background-image" src="../../images/bg.png"></image>

<!-- Boss图片 -->
<image class="boss-image" src="{{bossImageSrc}}"></image>

<!-- 弹弓 -->
<image class="slingshot-image" src="../../images/slingshot.png"></image>

<!-- 大粪 -->
<image class="poop-image" src="../../images/poop.png"></image>

<!-- 平台 -->
<image class="platform-image" src="../../images/platform.png"></image>

<!-- 轨迹点 -->
<view class="trajectory-dot" wx:for="{{trajectoryPoints}}" wx:key="index">
  <image src="../../images/dot.png"></image>
</view>
```

### 在JavaScript中使用
```javascript
// Boss状态切换
updateBossImage: function() {
  let imageSrc = '../../images/boss-normal.png';
  
  if (this.boss.isHit) {
    imageSrc = '../../images/boss-hit.png';
  } else if (this.boss.hasHelmet) {
    imageSrc = '../../images/boss-helmet.png';
  }
  
  this.setData({ bossImageSrc: imageSrc });
}
```

## 📊 素材统计

- **总文件数量**: 10个PNG文件
- **总文件大小**: 约8.4MB
- **图片格式**: PNG (支持透明通道)
- **设计风格**: 卡通可爱风格
- **命名规范**: 英文小写+连字符

## ⚠️ 重要说明

1. **路径格式**: 统一使用相对路径 `../../images/`
2. **文件命名**: 全部使用英文，避免中文兼容性问题
3. **大小写**: 文件名全部小写，使用连字符分隔
4. **扩展名**: 统一使用 `.png` 格式

## 🔄 更新记录

- **2024-05-27**: 完成所有文件的中英文重命名
- **2024-05-27**: 更新所有代码中的文件引用
- **2024-05-27**: 验证所有图片加载正常

---
**状态**: ✅ 已完成  
**最后更新**: 2024-05-27 